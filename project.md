# 基于图神经网络和扩散模型的点阵结构性能预测与设计系统

## 项目概述

本项目旨在开发一个统一的深度学习系统，能够：
1. **性能预测**：基于点阵结构的图表示预测其力学性能
2. **结构生成**：根据目标性能需求生成对应的点阵结构
3. **统一架构**：使用同一个模型架构同时支持预测和生成任务

## 数据分析

### 数据文件结构
- **node.csv**: 节点信息 (lattice_name, node_id, x, y, z)
- **edge.csv**: 边连接信息 (lattice_name, source_node, target_node)  
- **lattice.csv**: 性能数据 (43个点阵结构，22个性能指标)

### 数据维度分析
根据lattice.csv的实际结构，共包含28个特征列：

#### 结构标识 (1列)
- lattice_name: 点阵结构名称

#### 结构参数 (4列)
- num_nodes, num_edges, avg_connectivity, has_overlapping_bars

#### 晶胞参数 (6列)
- unit_cell_a, unit_cell_b, unit_cell_c, unit_cell_alpha, unit_cell_beta, unit_cell_gamma

#### 智能列分类 (完全自动识别) - **主要预测目标**
通过数据分析自动分类：
- **标识符列**: 字符串类型，唯一值多，用于标识样本
- **结构参数列**: 整数类型或离散值，描述图结构特征
- **性能指标列**: 连续数值类型，具有统计意义的目标变量

**性能预测维度**: 完全动态确定 (基于智能分析结果)
**条件生成维度**: 与性能预测维度完全一致

### 完全数据驱动设计原则
- **零预定义**: 不使用任何预定义的列名、关键字或模式匹配
- **智能分析**: 通过数据类型、统计特征、关联性等自动识别性能指标
- **特征驱动**: 基于数据本身的特征来区分标识符、结构参数和性能指标
- **完全自适应**: 模型架构100%根据数据分析结果动态调整

## 核心技术架构

### 1. 统一模型设计
```
输入图结构 → 图编码器 → 潜在表示 → 分支处理
                                    ├── 性能预测分支
                                    └── 扩散生成分支
```

### 2. 图神经网络编码器
- **架构**: GraphSAGE/GAT + 残差连接
- **输入特征**: 节点3D坐标 (x, y, z)
- **图结构**: 边的连接关系
- **输出**: 图级别的潜在表示

### 3. 性能预测分支
- **流程**: 图编码器 → 全局池化 → MLP → 性能输出
- **输出**: N维性能向量 (N由配置动态确定)
- **损失函数**: MSE Loss
- **自适应**: 输出层维度根据数据集自动调整

### 4. 扩散模型分支
- **类型**: 条件扩散模型 (Conditional DDPM)
- **扩散空间**: 节点坐标空间
- **条件输入**: N维目标性能向量 (与预测维度一致)
- **去噪网络**: GNN + MLP
- **损失函数**: DDPM Loss
- **自适应**: 条件嵌入维度根据性能指标数量自动调整

## 代码架构设计

### 目录结构
```
├── data_preprocessing.py      # 数据预处理和图构建
├── models/
│   ├── gnn_encoder.py        # 图神经网络编码器
│   ├── diffusion_model.py    # 扩散模型实现
│   └── unified_model.py      # 统一的预测+生成模型
├── training/
│   └── trainer.py            # 训练循环和损失计算
├── utils/
│   ├── metrics.py            # 评估指标
│   └── visualization.py     # 结果可视化
├── inference/
│   └── predictor.py          # 推理接口
├── configs/
│   └── config.yaml           # 主配置文件（不包含性能指标配置）
└── main.py                   # 主训练脚本
```

### 核心类设计

#### 1. LatticeDataset类
```python
class LatticeDataset(Dataset):
    """点阵数据集类，将CSV数据转换为PyG图对象"""

    def __init__(self, data_dir):
        # 使用智能列分类器
        self.classifier = IntelligentColumnClassifier(
            os.path.join(data_dir, 'lattice.csv')
        )

        # 打印分类报告（可选）
        self.classifier.print_classification_report()

        # 加载所有数据
        self.node_df = pd.read_csv(os.path.join(data_dir, 'node.csv'))
        self.edge_df = pd.read_csv(os.path.join(data_dir, 'edge.csv'))
        self.lattice_df = self.classifier.lattice_df

        # 构建图数据
        self.graphs = self._build_graphs()

    @property
    def performance_dim(self):
        """动态返回性能指标维度"""
        return self.classifier.performance_dim

    @property
    def performance_columns(self):
        """返回性能指标列名列表"""
        return self.classifier.get_performance_names()

    def get_performance_data(self):
        """获取性能数据"""
        return self.classifier.get_performance_data()

    def get_column_classification(self):
        """获取完整的列分类结果"""
        return self.classifier.get_column_classification()
```

#### 2. GraphEncoder类
```python
class GraphEncoder(nn.Module):
    """图神经网络编码器"""
    - 多层图卷积网络
    - 残差连接和层归一化
    - 全局图池化
```

#### 3. GraphDiffusionModel类
```python
class GraphDiffusionModel(nn.Module):
    """图扩散模型"""
    - 噪声调度器
    - 条件去噪网络
    - 前向和反向扩散过程
```

#### 4. UnifiedLatticeModel类
```python
class UnifiedLatticeModel(nn.Module):
    """统一的预测+生成模型"""
    def __init__(self, performance_dim, ...):
        """
        Args:
            performance_dim: 动态从数据集获取的性能维度
        """
    - 共享图编码器
    - 性能预测头 (输出维度=performance_dim)
    - 扩散生成模块 (条件维度=performance_dim)
    - 联合训练接口
```

## 训练策略

### 多阶段训练
1. **阶段1**: 预训练性能预测模型，优化图编码器
2. **阶段2**: 固定编码器，训练扩散模型
3. **阶段3**: 端到端联合微调

### 损失函数
```python
total_loss = λ₁ * prediction_loss + λ₂ * diffusion_loss
```

### 数据划分
- 训练集: 30个点阵结构 (~70%)
- 验证集: 8个点阵结构 (~20%)  
- 测试集: 5个点阵结构 (~10%)

## 智能数据分析和列分类

### 完全自动的列分类器
```python
class IntelligentColumnClassifier:
    """基于数据特征的智能列分类器"""

    def __init__(self, lattice_csv_path):
        self.lattice_df = pd.read_csv(lattice_csv_path)
        self.column_analysis = self._analyze_all_columns()
        self.performance_columns = self._identify_performance_columns()

    def _analyze_all_columns(self):
        """分析所有列的数据特征"""
        analysis = {}

        for col in self.lattice_df.columns:
            col_data = self.lattice_df[col]

            analysis[col] = {
                'dtype': str(col_data.dtype),
                'is_numeric': pd.api.types.is_numeric_dtype(col_data),
                'unique_count': col_data.nunique(),
                'unique_ratio': col_data.nunique() / len(col_data),
                'has_nulls': col_data.isnull().any(),
                'is_constant': col_data.nunique() <= 1,
                'is_binary': col_data.nunique() == 2,
                'is_categorical': self._is_categorical(col_data),
                'variance': col_data.var() if pd.api.types.is_numeric_dtype(col_data) else 0,
                'range': col_data.max() - col_data.min() if pd.api.types.is_numeric_dtype(col_data) else 0
            }

        return analysis

    def _is_categorical(self, series):
        """判断是否为分类变量"""
        if not pd.api.types.is_numeric_dtype(series):
            return True

        # 如果是整数且唯一值较少，可能是分类变量
        if pd.api.types.is_integer_dtype(series) and series.nunique() < 10:
            return True

        return False

    def _identify_performance_columns(self):
        """基于数据特征识别性能指标列"""
        performance_columns = []

        for col, features in self.column_analysis.items():
            # 性能指标的特征：
            # 1. 必须是数值类型
            # 2. 不是常量（有变化）
            # 3. 不是简单的计数或标识符
            # 4. 具有统计意义的连续值

            if (features['is_numeric'] and
                not features['is_constant'] and
                not features['is_categorical'] and
                features['variance'] > 0 and
                features['unique_ratio'] > 0.1):  # 避免简单的枚举值

                performance_columns.append(col)

        return sorted(performance_columns)

    def get_column_classification(self):
        """获取完整的列分类结果"""
        classification = {
            'identifier_columns': [],
            'structural_columns': [],
            'performance_columns': self.performance_columns
        }

        for col, features in self.column_analysis.items():
            if col in self.performance_columns:
                continue

            if not features['is_numeric']:
                classification['identifier_columns'].append(col)
            elif features['is_categorical'] or features['unique_count'] < 20:
                classification['structural_columns'].append(col)

        return classification

    @property
    def performance_dim(self):
        """返回性能指标维度"""
        return len(self.performance_columns)

    def get_performance_data(self):
        """获取性能数据矩阵"""
        return self.lattice_df[self.performance_columns].values

    def get_performance_names(self):
        """获取性能指标名称列表"""
        return self.performance_columns.copy()

    def print_classification_report(self):
        """打印列分类报告"""
        classification = self.get_column_classification()

        print("=== 智能列分类报告 ===")
        print(f"标识符列 ({len(classification['identifier_columns'])}): {classification['identifier_columns']}")
        print(f"结构参数列 ({len(classification['structural_columns'])}): {classification['structural_columns']}")
        print(f"性能指标列 ({len(classification['performance_columns'])}): {classification['performance_columns']}")
        print(f"性能预测维度: {self.performance_dim}")
```

## 技术难点与解决方案

### 1. 图大小不一致
- **问题**: 不同点阵的节点数差异大(8-26个节点)
- **解决**: 使用PyG的动态批处理和全局池化

### 2. 扩散模型适配图结构
- **问题**: 传统扩散模型用于图像，需适配图结构
- **解决**: 在节点坐标空间进行扩散，保持拓扑结构

### 3. 条件生成实现
- **问题**: 根据性能需求生成对应结构
- **解决**: 将性能向量作为条件嵌入到去噪网络

### 4. 小数据集挑战
- **问题**: 仅43个样本，容易过拟合
- **解决**: 数据增强、正则化、迁移学习

### 5. 性能指标变化适应
- **问题**: 用户可能调整lattice.csv中的性能指标
- **解决**: 完全数据驱动的检测系统，运行时自动识别所有性能列

## 评估指标

### 性能预测评估
- Mean Absolute Error (MAE)
- Root Mean Square Error (RMSE)  
- R² Score
- 各性能指标的单独评估

### 生成质量评估
- 结构有效性 (图连通性、几何合理性)
- 性能匹配度 (生成结构的预测性能与目标的差异)
- 结构多样性 (生成结构的变化程度)
- 新颖性 (与训练集的相似度)

## 依赖项要求

### 核心框架
- torch >= 1.12.0
- torch-geometric >= 2.1.0
- torch-scatter, torch-sparse, torch-cluster

### 数据处理
- pandas >= 1.3.0
- numpy >= 1.21.0
- scikit-learn >= 1.0.0

### 可视化
- matplotlib >= 3.5.0
- plotly >= 5.0.0 (3D可视化)
- seaborn >= 0.11.0

### 其他工具
- tqdm (进度条)
- hydra-core (配置管理)
- wandb (实验跟踪，可选)

## 实施计划

### 第一阶段：数据预处理和基础架构 (2-3天)
- [ ] 实现IntelligentColumnClassifier智能分类器
- [ ] 实现LatticeDataset类 (零预定义，完全智能)
- [ ] 构建图数据预处理流程
- [ ] 实现基础GraphEncoder (自适应输出维度)
- [ ] 验证数据流和智能分类系统

### 第二阶段：性能预测模型 (2-3天)  
- [ ] 完善性能预测分支
- [ ] 训练和验证预测模型
- [ ] 评估预测精度
- [ ] 模型架构优化

### 第三阶段：扩散模型实现 (3-4天)
- [ ] 实现GraphDiffusionModel
- [ ] 设计条件生成机制  
- [ ] 训练扩散模型
- [ ] 验证生成质量

### 第四阶段：统一模型和联合训练 (2-3天)
- [ ] 整合UnifiedLatticeModel
- [ ] 实现联合训练策略
- [ ] 端到端优化
- [ ] 超参数调优

### 第五阶段：评估和可视化 (1-2天)
- [ ] 全面模型评估
- [ ] 结果可视化系统
- [ ] 使用文档编写
- [ ] 代码优化清理

## 预期成果

1. **高精度性能预测**: 在测试集上达到较高的预测精度
2. **有效结构生成**: 能够根据性能需求生成合理的点阵结构
3. **统一模型架构**: 一个模型同时支持预测和生成
4. **可扩展框架**: 易于扩展到其他类型的结构设计问题

## 风险评估

### 高风险
- 小数据集可能导致过拟合
- 扩散模型在图结构上的效果不确定

### 中风险  
- 联合训练的稳定性
- 生成结构的物理合理性

### 低风险
- 性能预测任务相对成熟
- 图神经网络技术较为稳定

---

## 使用示例：零预定义的智能数据分析

### 场景1：任意列名的数据
无论您的lattice.csv使用什么列名：
```csv
structure_id,node_count,edge_count,property_A,property_B,property_C
sample_001,8,12,0.123,0.456,0.789
sample_002,10,15,0.234,0.567,0.890
```
**完全自动识别！** 智能分类器会：
- 识别`structure_id`为标识符列（字符串类型）
- 识别`node_count`, `edge_count`为结构参数列（整数，离散值）
- 识别`property_A`, `property_B`, `property_C`为性能指标列（连续数值）

### 场景2：混合数据类型
```csv
name,type,size,stiffness,strength,weight,is_hollow
struct_A,cubic,large,12.34,56.78,9.01,True
struct_B,hex,small,23.45,67.89,10.12,False
```
**智能分析结果**：
- 标识符列：`name`
- 结构参数列：`type`, `size`, `is_hollow`
- 性能指标列：`stiffness`, `strength`, `weight`

### 场景3：完全不同的研究领域
如果您研究的不是点阵结构，而是其他材料：
```csv
material_id,crystal_system,bandgap,conductivity,magnetization
mat_001,fcc,2.1,0.85,1.23
mat_002,bcc,1.8,0.92,0.87
```
**无缝适应！** 代码会自动识别`bandgap`, `conductivity`, `magnetization`为性能指标。

### 智能分类算法
代码运行时的分析流程：
1. **数据类型分析**：字符串 vs 数值类型
2. **统计特征分析**：唯一值比例、方差、范围等
3. **分类变量检测**：整数且唯一值少的列
4. **性能指标识别**：连续数值、有变化、非分类的列
5. **自动分类**：标识符、结构参数、性能指标
6. **模型适应**：架构自动调整到检测的维度

**核心优势**：无论您的数据格式、列名、研究领域如何变化，代码都能智能适应！

---

**注意**: 本项目具有一定的研究性质和技术挑战，需要在实施过程中根据实际效果调整策略。建议采用迭代开发的方式，先实现基础功能，再逐步优化和扩展。完全数据驱动的设计确保了代码的最大灵活性，您可以随时调整lattice.csv中的性能指标，无需修改任何代码。
