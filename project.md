# 基于图神经网络和扩散模型的点阵结构性能预测与设计系统

## 项目概述

本项目旨在开发一个统一的深度学习系统，能够：
1. **性能预测**：基于点阵结构的图表示预测其力学性能
2. **结构生成**：根据目标性能需求生成对应的点阵结构
3. **统一架构**：使用同一个模型架构同时支持预测和生成任务

## 数据分析

### 数据文件结构
- **node.csv**: 节点信息 (lattice_name, node_id, x, y, z)
- **edge.csv**: 边连接信息 (lattice_name, source_node, target_node)  
- **lattice.csv**: 性能数据 (43个点阵结构，22个性能指标)

### 数据维度分析
根据lattice.csv的实际结构，共包含28个特征列：

#### 结构标识 (1列)
- lattice_name: 点阵结构名称

#### 结构参数 (4列)
- num_nodes, num_edges, avg_connectivity, has_overlapping_bars

#### 晶胞参数 (6列)
- unit_cell_a, unit_cell_b, unit_cell_c, unit_cell_alpha, unit_cell_beta, unit_cell_gamma

#### 力学性能指标 (12列) - **主要预测目标**
- 弹性模量: mech_Ex, mech_Ey, mech_Ez
- 剪切模量: mech_Gyz, mech_Gxz, mech_Gxy
- 泊松比: mech_nuyz, mech_nuxz, mech_nuxy, mech_nuzy, mech_nuzx, mech_nuyx

#### 缩放参数 (6列)
- scaling_Cx, scaling_Cy, scaling_Cz, scaling_nx, scaling_ny, scaling_nz

**性能预测维度**: 12维 (力学性能指标)
**条件生成维度**: 12维 (目标力学性能向量)

## 核心技术架构

### 1. 统一模型设计
```
输入图结构 → 图编码器 → 潜在表示 → 分支处理
                                    ├── 性能预测分支
                                    └── 扩散生成分支
```

### 2. 图神经网络编码器
- **架构**: GraphSAGE/GAT + 残差连接
- **输入特征**: 节点3D坐标 (x, y, z)
- **图结构**: 边的连接关系
- **输出**: 图级别的潜在表示

### 3. 性能预测分支
- **流程**: 图编码器 → 全局池化 → MLP → 性能输出
- **输出**: 12维力学性能向量 (动态从lattice.csv确定)
- **损失函数**: MSE Loss

### 4. 扩散模型分支
- **类型**: 条件扩散模型 (Conditional DDPM)
- **扩散空间**: 节点坐标空间
- **条件输入**: 12维目标力学性能向量
- **去噪网络**: GNN + MLP
- **损失函数**: DDPM Loss

## 代码架构设计

### 目录结构
```
├── data_preprocessing.py      # 数据预处理和图构建
├── models/
│   ├── gnn_encoder.py        # 图神经网络编码器
│   ├── diffusion_model.py    # 扩散模型实现
│   └── unified_model.py      # 统一的预测+生成模型
├── training/
│   └── trainer.py            # 训练循环和损失计算
├── utils/
│   ├── metrics.py            # 评估指标
│   └── visualization.py     # 结果可视化
├── inference/
│   └── predictor.py          # 推理接口
├── configs/
│   └── config.yaml           # 配置文件
└── main.py                   # 主训练脚本
```

### 核心类设计

#### 1. LatticeDataset类
```python
class LatticeDataset(Dataset):
    """点阵数据集类，将CSV数据转换为PyG图对象"""
    - 加载node.csv, edge.csv, lattice.csv
    - 构建PyTorch Geometric Data对象
    - 数据标准化和增强
```

#### 2. GraphEncoder类
```python
class GraphEncoder(nn.Module):
    """图神经网络编码器"""
    - 多层图卷积网络
    - 残差连接和层归一化
    - 全局图池化
```

#### 3. GraphDiffusionModel类
```python
class GraphDiffusionModel(nn.Module):
    """图扩散模型"""
    - 噪声调度器
    - 条件去噪网络
    - 前向和反向扩散过程
```

#### 4. UnifiedLatticeModel类
```python
class UnifiedLatticeModel(nn.Module):
    """统一的预测+生成模型"""
    - 共享图编码器
    - 性能预测头
    - 扩散生成模块
    - 联合训练接口
```

## 训练策略

### 多阶段训练
1. **阶段1**: 预训练性能预测模型，优化图编码器
2. **阶段2**: 固定编码器，训练扩散模型
3. **阶段3**: 端到端联合微调

### 损失函数
```python
total_loss = λ₁ * prediction_loss + λ₂ * diffusion_loss
```

### 数据划分
- 训练集: 30个点阵结构 (~70%)
- 验证集: 8个点阵结构 (~20%)  
- 测试集: 5个点阵结构 (~10%)

## 技术难点与解决方案

### 1. 图大小不一致
- **问题**: 不同点阵的节点数差异大(8-26个节点)
- **解决**: 使用PyG的动态批处理和全局池化

### 2. 扩散模型适配图结构
- **问题**: 传统扩散模型用于图像，需适配图结构
- **解决**: 在节点坐标空间进行扩散，保持拓扑结构

### 3. 条件生成实现
- **问题**: 根据性能需求生成对应结构
- **解决**: 将性能向量作为条件嵌入到去噪网络

### 4. 小数据集挑战
- **问题**: 仅43个样本，容易过拟合
- **解决**: 数据增强、正则化、迁移学习

## 评估指标

### 性能预测评估
- Mean Absolute Error (MAE)
- Root Mean Square Error (RMSE)  
- R² Score
- 各性能指标的单独评估

### 生成质量评估
- 结构有效性 (图连通性、几何合理性)
- 性能匹配度 (生成结构的预测性能与目标的差异)
- 结构多样性 (生成结构的变化程度)
- 新颖性 (与训练集的相似度)

## 依赖项要求

### 核心框架
- torch >= 1.12.0
- torch-geometric >= 2.1.0
- torch-scatter, torch-sparse, torch-cluster

### 数据处理
- pandas >= 1.3.0
- numpy >= 1.21.0
- scikit-learn >= 1.0.0

### 可视化
- matplotlib >= 3.5.0
- plotly >= 5.0.0 (3D可视化)
- seaborn >= 0.11.0

### 其他工具
- tqdm (进度条)
- hydra-core (配置管理)
- wandb (实验跟踪，可选)

## 实施计划

### 第一阶段：数据预处理和基础架构 (2-3天)
- [ ] 实现LatticeDataset类
- [ ] 构建图数据预处理流程
- [ ] 实现基础GraphEncoder
- [ ] 验证数据流

### 第二阶段：性能预测模型 (2-3天)  
- [ ] 完善性能预测分支
- [ ] 训练和验证预测模型
- [ ] 评估预测精度
- [ ] 模型架构优化

### 第三阶段：扩散模型实现 (3-4天)
- [ ] 实现GraphDiffusionModel
- [ ] 设计条件生成机制  
- [ ] 训练扩散模型
- [ ] 验证生成质量

### 第四阶段：统一模型和联合训练 (2-3天)
- [ ] 整合UnifiedLatticeModel
- [ ] 实现联合训练策略
- [ ] 端到端优化
- [ ] 超参数调优

### 第五阶段：评估和可视化 (1-2天)
- [ ] 全面模型评估
- [ ] 结果可视化系统
- [ ] 使用文档编写
- [ ] 代码优化清理

## 预期成果

1. **高精度性能预测**: 在测试集上达到较高的预测精度
2. **有效结构生成**: 能够根据性能需求生成合理的点阵结构
3. **统一模型架构**: 一个模型同时支持预测和生成
4. **可扩展框架**: 易于扩展到其他类型的结构设计问题

## 风险评估

### 高风险
- 小数据集可能导致过拟合
- 扩散模型在图结构上的效果不确定

### 中风险  
- 联合训练的稳定性
- 生成结构的物理合理性

### 低风险
- 性能预测任务相对成熟
- 图神经网络技术较为稳定

---

**注意**: 本项目具有一定的研究性质和技术挑战，需要在实施过程中根据实际效果调整策略。建议采用迭代开发的方式，先实现基础功能，再逐步优化和扩展。
